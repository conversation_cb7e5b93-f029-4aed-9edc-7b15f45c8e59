{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T07:46:39.485Z"}
{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T08:06:40.332Z"}
{"context":"SimpleMEVBot.start","level":"error","message":"[SimpleMEVBot.start] Insufficient wallet balance for MEV operations","stack":"Error: Insufficient wallet balance for MEV operations\n    at SimpleMEVBot.checkWalletBalance (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:54:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SimpleMEVBot.start (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:30:13)\n    at async runSimpleBot (/Users/<USER>/Coden/mev/bo1/dist/simple-bot.js:89:9)\n    at async main (/Users/<USER>/Coden/mev/bo1/dist/index.js:9:9)","timestamp":"2025-05-31T08:08:38.637Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 2, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 2, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:25:55.692Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 11, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 11, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.021Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 13, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 13, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.022Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 19, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 19, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.374Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 23, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 23, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.375Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 27, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 27, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.492Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 29, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 29, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.493Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 35, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 35, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.733Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 39, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 39, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x3850c7bd\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.734Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 43, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 43, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.853Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 45, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 45, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.853Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 47, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 47, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.971Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 49, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee82000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 49, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee82000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:38.971Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 51, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 51, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.089Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 53, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 53, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.090Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 55, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 55, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.207Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 57, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 57, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.207Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 59, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 59, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.325Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 61, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 61, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.325Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 63, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 63, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.443Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 65, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 65, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a357\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.444Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 67, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 67, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba060000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.562Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 69, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 69, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.562Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 75, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 75, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c7238000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.803Z"}
{"context":"PoolManager.loadPoolData(******************************************)","level":"error","message":"[PoolManager.loadPoolData(******************************************)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 77, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x0902f1ac\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 77, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x0902f1ac\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:39.803Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 95, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 95, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.407Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v3)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v3)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 97, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 97, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0x1698ee820000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000000000000000000000000000000000000000000bb8\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.407Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 99, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 99, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.525Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 101, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 101, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b14\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.525Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 103, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" }, { \"code\": -32005, \"data\": { \"see\": \"https://infura.io/dashboard\" }, \"message\": \"Too Many Requests\" } ], info={ \"payload\": { \"id\": 103, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a43905000000000000000000000000ff34b3d4aee8ddcd6f9afffb6fe49bd371b8a3570000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:259:72\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-31T08:26:40.645Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"8db9282127e30033172afde8182c4d5d\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 1, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"8db9282127e30033172afde8182c4d5d\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 1, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:653:41)\n    at JsonRpcProvider._detectNetwork (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:386:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async JsonRpcProvider.getNetwork (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/abstract-provider.js:556:21)\n    at async FlashbotsBundleProvider.create (/Users/<USER>/Coden/mev/bo1/node_modules/@flashbots/ethers-provider-bundle/build/index.js:90:35)\n    at async BundleSimulator.initializeFlashbots (/Users/<USER>/Coden/mev/bo1/dist/simulation/simulator.js:20:38)","timestamp":"2025-06-13T21:49:50.913Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"0d2d661167d69cc08c92900a616d29e3\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 1, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"0d2d661167d69cc08c92900a616d29e3\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 1, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:653:41)\n    at JsonRpcProvider._detectNetwork (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:386:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async JsonRpcProvider.getNetwork (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/abstract-provider.js:556:21)\n    at async FlashbotsBundleProvider.create (/Users/<USER>/Coden/mev/bo1/node_modules/@flashbots/ethers-provider-bundle/build/index.js:90:35)\n    at async BundleSimulator.initializeFlashbots (/Users/<USER>/Coden/mev/bo1/dist/simulation/simulator.js:20:38)","timestamp":"2025-06-13T21:52:03.094Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:534:29)\n    at async FlashloanStrategy.findSpecificDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:287:33)\n    at async FlashloanStrategy.findCrossDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:205:35)","timestamp":"2025-06-13T23:19:08.973Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:534:29)\n    at async FlashloanStrategy.findSpecificDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:287:33)\n    at async FlashloanStrategy.findCrossDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:205:35)","timestamp":"2025-06-13T23:19:09.001Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateFlashloanGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:560:29)\n    at async FlashloanStrategy.buildEnhancedFlashloanRoute (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:319:34)\n    at async FlashloanStrategy.scanForFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:136:48)","timestamp":"2025-06-13T23:19:09.471Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateFlashloanGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:560:29)\n    at async FlashloanStrategy.buildEnhancedFlashloanRoute (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:319:34)\n    at async FlashloanStrategy.scanForFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:136:48)","timestamp":"2025-06-13T23:19:09.900Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006353070000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BalancerFlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:312:29)\n    at async BalancerFlashloanStrategy.findArbitrageOpportunity (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:218:33)\n    at async BalancerFlashloanStrategy.scanForBalancerFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:153:40)","timestamp":"2025-06-13T23:19:10.560Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:562:29)\n    at async FlashloanStrategy.findSpecificDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:287:33)\n    at async FlashloanStrategy.findCrossDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:205:35)","timestamp":"2025-06-13T23:26:57.946Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:562:29)\n    at async FlashloanStrategy.findSpecificDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:287:33)\n    at async FlashloanStrategy.findCrossDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:205:35)","timestamp":"2025-06-13T23:26:57.971Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateFlashloanGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:588:29)\n    at async FlashloanStrategy.buildEnhancedFlashloanRoute (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:319:34)\n    at async FlashloanStrategy.scanForFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:136:48)","timestamp":"2025-06-13T23:26:58.482Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateFlashloanGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:588:29)\n    at async FlashloanStrategy.buildEnhancedFlashloanRoute (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:319:34)\n    at async FlashloanStrategy.scanForFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:136:48)","timestamp":"2025-06-13T23:26:59.384Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1005062620000001\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BalancerFlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:312:29)\n    at async BalancerFlashloanStrategy.findArbitrageOpportunity (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:218:33)\n    at async BalancerFlashloanStrategy.scanForBalancerFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:153:40)","timestamp":"2025-06-13T23:26:59.962Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:562:29)\n    at async FlashloanStrategy.findSpecificDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:287:33)\n    at async FlashloanStrategy.findCrossDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:205:35)","timestamp":"2025-06-13T23:28:40.460Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:562:29)\n    at async FlashloanStrategy.findSpecificDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:287:33)\n    at async FlashloanStrategy.findCrossDexArbitrage (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:205:35)","timestamp":"2025-06-13T23:28:40.490Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateFlashloanGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:588:29)\n    at async FlashloanStrategy.buildEnhancedFlashloanRoute (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:319:34)\n    at async FlashloanStrategy.scanForFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:136:48)","timestamp":"2025-06-13T23:28:40.956Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FlashloanStrategy.estimateFlashloanGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:588:29)\n    at async FlashloanStrategy.buildEnhancedFlashloanRoute (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:319:34)\n    at async FlashloanStrategy.scanForFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/flashloan.js:136:48)","timestamp":"2025-06-13T23:28:41.406Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)","stack":"RangeError: too many decimals for format (operation=\"fromString\", fault=\"underflow\", value=\"1.1006024870000002\", code=NUMERIC_FAULT, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:122:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:142:15)\n    at FixedNumber.fromString (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/fixednumber.js:499:32)\n    at Object.parseUnits (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/units.js:72:41)\n    at GasOptimizer.calculateOptimalStrategyAdvanced (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:70:43)\n    at GasOptimizer.getCurrentGasStrategy (/Users/<USER>/Coden/mev/bo1/dist/gas/optimizer.js:37:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BalancerFlashloanStrategy.estimateArbitrageGasCost (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:312:29)\n    at async BalancerFlashloanStrategy.findArbitrageOpportunity (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:218:33)\n    at async BalancerFlashloanStrategy.scanForBalancerFlashloanOpportunities (/Users/<USER>/Coden/mev/bo1/dist/strategies/balancer-flashloan.js:153:40)","timestamp":"2025-06-13T23:28:41.972Z"}
{"context":"FlashloanStrategy.executeWithHybridContract","level":"error","message":"[FlashloanStrategy.executeWithHybridContract] execution reverted: \"27\" (action=\"estimateGas\", data=\"0x08c379a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000023237000000000000000000000000000000000000000000000000000000000000\", reason=\"27\", transaction={ \"data\": \"0x9bc62f7a000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b1400000000000000000000000000000000000000000000021e19e0c9bab2400000000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c723800000000000000000000000086dcd3293c53cf8efd7303b57beb2a3f671dde980000000000000000000000003bfa4769fb09eefc5a80d6e87c3b9c650f7ae48e0000000000000000000000000000000000000000000000000000000000000bb800000000000000000000000000000000000000000000000a922936299afd92000000000000000000000000000000000000000000000000000000000000000000\", \"from\": \"******************************************\", \"to\": \"******************************************\" }, invocation=null, revert={ \"args\": [ \"27\" ], \"name\": \"Error\", \"signature\": \"Error(string)\" }, code=CALL_EXCEPTION, version=6.7.1)","stack":"Error: execution reverted: \"27\" (action=\"estimateGas\", data=\"0x08c379a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000023237000000000000000000000000000000000000000000000000000000000000\", reason=\"27\", transaction={ \"data\": \"0x9bc62f7a000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b1400000000000000000000000000000000000000000000021e19e0c9bab2400000000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000fff9976782d46cc05630d1f6ebab18b2324d6b140000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c723800000000000000000000000086dcd3293c53cf8efd7303b57beb2a3f671dde980000000000000000000000003bfa4769fb09eefc5a80d6e87c3b9c650f7ae48e0000000000000000000000000000000000000000000000000000000000000bb800000000000000000000000000000000000000000000000a922936299afd92000000000000000000000000000000000000000000000000000000000000000000\", \"from\": \"******************************************\", \"to\": \"******************************************\" }, invocation=null, revert={ \"args\": [ \"27\" ], \"name\": \"Error\", \"signature\": \"Error(string)\" }, code=CALL_EXCEPTION, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/utils/errors.js:125:21)\n    at getBuiltinCallException (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/abi/abi-coder.js:104:37)\n    at AbiCoder.getBuiltinCallException (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/abi/abi-coder.js:201:16)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:599:43)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:268:45\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13T23:32:11.450Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 247, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 247, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.873Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 249, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 249, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.873Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 251, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 251, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 253, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 253, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 255, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 255, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 257, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 257, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 259, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 259, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 261, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 261, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 263, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"f0505c0d4a772de3d85618228798aee2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 263, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.874Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.944Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.944Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.944Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3c74a0f316371e25ac92649a48e4639c\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 379, \"jsonrpc\": \"2.0\", \"method\": \"eth_getBlockByNumber\", \"params\": [ \"latest\", false ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:49.945Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"********************************\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 24, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"********************************\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 24, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.053Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 380, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 380, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.430Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 383, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 383, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.430Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 385, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 385, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.430Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 387, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 387, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.430Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 389, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 389, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.430Z"}
{"context":"GasOptimizer.getCurrentGasStrategy","level":"error","message":"[GasOptimizer.getCurrentGasStrategy] could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 391, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)","stack":"Error: could not coalesce error (error={ \"code\": -32090, \"data\": { \"trace_id\": \"29587ac26f192a1d74266d1262059090\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, payload={ \"id\": 391, \"jsonrpc\": \"2.0\", \"method\": \"eth_chainId\", \"params\": [  ] }, code=UNKNOWN_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:976:25)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:35:50.430Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing revert data (action=\"call\", data=null, reason=null, transaction={ \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, invocation=null, revert=null, code=CALL_EXCEPTION, version=6.7.1)","stack":"Error: missing revert data (action=\"call\", data=null, reason=null, transaction={ \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, invocation=null, revert=null, code=CALL_EXCEPTION, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at getBuiltinCallException (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/abi/abi-coder.ts:118:21)\n    at Function.getBuiltinCallException (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/abi/abi-coder.ts:230:16)\n    at JsonRpcProvider.getRpcError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:906:32)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:526:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:25.892Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"14770bdbd3ab601f461b359c4b97daac\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 112, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"14770bdbd3ab601f461b359c4b97daac\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 112, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:26.399Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"592fa13cae124ae5e0866823c9278cc2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 114, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"592fa13cae124ae5e0866823c9278cc2\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 114, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:26.522Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"5b31dd927ad9cd46ae98a1b5d8e18e08\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 116, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"5b31dd927ad9cd46ae98a1b5d8e18e08\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 116, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:26.647Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"f86ff9f609af846d495910b80009029e\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 118, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"f86ff9f609af846d495910b80009029e\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 118, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:26.773Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"36d501fc89c338e3691cb1e429a8efd1\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 122, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"36d501fc89c338e3691cb1e429a8efd1\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 122, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:27.020Z"}
{"context":"PoolManager.getPool(******************************************, ******************************************, uniswap-v2)","level":"error","message":"[PoolManager.getPool(******************************************, ******************************************, uniswap-v2)] missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3e84b1756286d1119b340bd7765f486d\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 124, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)","stack":"Error: missing response for request (value=[ { \"error\": { \"code\": -32090, \"data\": { \"trace_id\": \"3e84b1756286d1119b340bd7765f486d\" }, \"message\": \"Too many requests, reason: call rate limit exhausted, retry in 10s\" }, \"id\": null, \"jsonrpc\": \"2.0\" } ], info={ \"payload\": { \"id\": 124, \"jsonrpc\": \"2.0\", \"method\": \"eth_call\", \"params\": [ { \"data\": \"0xe6a439050000000000000000000000001c7d4b196cb0c7b01d743fbc6116a902379c72380000000000000000000000007169d38820dfd117c3fa1f22a697dba58d90ba06\", \"to\": \"******************************************\" }, \"latest\" ] } }, code=BAD_DATA, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:516:56\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-13T23:42:27.143Z"}
{"context":"MempoolMonitor.setupProviders","level":"error","message":"[MempoolMonitor.setupProviders] ","stack":"AggregateError\n    at internalConnectMultiple (node:net:1122:18)\n    at afterConnectMultiple (node:net:1689:7)","timestamp":"2025-06-14T07:38:49.255Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] ","stack":"AggregateError\n    at internalConnectMultiple (node:net:1122:18)\n    at afterConnectMultiple (node:net:1689:7)","timestamp":"2025-06-14T07:38:49.256Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] ","stack":"AggregateError\n    at internalConnectMultiple (node:net:1122:18)\n    at afterConnectMultiple (node:net:1689:7)","timestamp":"2025-06-14T07:38:49.256Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] ","stack":"AggregateError\n    at internalConnectMultiple (node:net:1122:18)\n    at afterConnectMultiple (node:net:1689:7)","timestamp":"2025-06-14T07:38:49.257Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] ","stack":"AggregateError\n    at internalConnectMultiple (node:net:1122:18)\n    at afterConnectMultiple (node:net:1689:7)","timestamp":"2025-06-14T07:38:49.257Z"}
{"context":"BundleSimulator.initializeFlashbots","level":"error","message":"[BundleSimulator.initializeFlashbots] ","stack":"AggregateError\n    at internalConnectMultiple (node:net:1122:18)\n    at afterConnectMultiple (node:net:1689:7)","timestamp":"2025-06-14T07:38:49.257Z"}
{"context":"main","level":"error","message":"[main] server response 524  (request={  }, response={  }, error=null, code=SERVER_ERROR, version=6.7.1)","stack":"Error: server response 524  (request={  }, response={  }, error=null, code=SERVER_ERROR, version=6.7.1)\n    at makeError (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:685:21)\n    at assert (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/errors.ts:702:25)\n    at FetchResponse.assertOk (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/utils/fetch.ts:896:15)\n    at JsonRpcProvider._send (/Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:1160:18)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Coden/mev/bo1/node_modules/ethers/src.ts/providers/provider-jsonrpc.ts:500:40","timestamp":"2025-06-14T07:40:51.227Z"}
